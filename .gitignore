# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
.venv/
venv/
ENV/
env/

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Generated Content
generated/
media/
downloads_*/

# Data Directories
3b1b_data/
manim_rag_db/

# API Keys and Sensitive Files
*.key

# Temporary Files
*.tmp
*.temp
*.log

# Manim Cache
.manim_cache/

# Media Files
*.mp4
*.mp3
*.wav
*.avi
*.mov
*.gif

# Model Files
*.bin
*.safetensors
models/

# Database Files
*.db
*.sqlite3
*.sqlite

# Backup Files
*.bak
*.backup

# React/Node.js
frontend_react/node_modules/
frontend_react/build/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test Files and Examples
test_*.py
test_*.mp4
test_*.mp3
working.txt
instructions.txt
prompts.txt
github_upload_commands.txt

# Development Files
new.py
api.py
api_client.py
