"""
Synchronized Text-to-Speech System
Generates audio with precise timing to match video animations
"""

import os
import json
import re
import tempfile
from pathlib import Path
from typing import List, Dict, Tuple
import pyttsx3
from pydub import AudioSegment
from pydub.silence import split_on_silence


class SynchronizedTTS:
    """Text-to-speech system with precise timing control"""
    
    def __init__(self):
        self.engine = None
        self._voice_config = None
        self._sentence_count = 0  # Track sentences processed
        self._initialize_engine()

    def _initialize_engine(self):
        """Initialize or reinitialize the TTS engine"""
        try:
            if self.engine:
                # Clean up existing engine
                try:
                    self.engine.stop()
                except:
                    pass
                del self.engine

            # Create new engine instance
            self.engine = pyttsx3.init()
            self._configure_voice()
            print("🔄 TTS engine (re)initialized successfully")
        except Exception as e:
            print(f"⚠️ TTS engine initialization failed: {e}")
            self.engine = None

    def _configure_voice(self):
        """Configure TTS engine for optimal quality"""
        voices = self.engine.getProperty('voices')
        
        # Prefer English voices, especially <PERSON><PERSON> (female) or <PERSON> (male)
        preferred_voices = ['zira', 'david', 'english']
        selected_voice = None
        
        for voice in voices:
            voice_name = voice.name.lower()
            for preferred in preferred_voices:
                if preferred in voice_name:
                    selected_voice = voice.id
                    print(f"Selected voice: {voice.name}")
                    break
            if selected_voice:
                break
        
        if selected_voice:
            self.engine.setProperty('voice', selected_voice)
        
        # Set speech rate for natural delivery
        self.engine.setProperty('rate', 160)  # Slightly slower for educational content
        self.engine.setProperty('volume', 0.9)

    def cleanup(self):
        """Clean up TTS engine resources"""
        try:
            if self.engine:
                print("🧹 Cleaning up TTS engine...")
                self.engine.stop()
                del self.engine
                self.engine = None
                print("✅ TTS engine cleaned up successfully")
        except Exception as e:
            print(f"⚠️ TTS cleanup warning: {e}")

    def __del__(self):
        """Destructor to ensure cleanup"""
        self.cleanup()
    
    def generate_timed_audio(self, script: str, job_id: str, 
                           timing_info: Dict = None, target_duration: float = None) -> str:
        """
        Generate audio with precise timing to match video duration
        
        Args:
            script: The text to convert to speech
            job_id: Job identifier for output directory
            timing_info: Optional timing segments information
            target_duration: Target duration in seconds
        
        Returns:
            Path to generated audio file
        """
        base_path = Path(f"generated/{job_id}")
        base_path.mkdir(parents=True, exist_ok=True)
        
        # Extract sentences for speech
        sentences = self._extract_speech_sentences(script)
        
        if not sentences:
            print("No suitable sentences found for TTS conversion.")
            return ""
        
        print(f"🎵 Generating timed audio for {len(sentences)} sentences...")

        # Reset sentence counter for this job
        self._sentence_count = 0

        # Create temporary directory for audio segments
        with tempfile.TemporaryDirectory() as temp_dir:
            audio_segments = []

            # Generate audio for each sentence
            for i, sentence in enumerate(sentences):
                temp_file = os.path.join(temp_dir, f"sentence_{i:03d}.wav")

                print(f"Converting sentence {i+1}/{len(sentences)}: {sentence[:50]}...")

                # CRITICAL FIX: Reinitialize engine for each sentence to prevent hanging
                # This fixes the known pyttsx3 bug where it hangs on the second sentence
                if i > 0:  # Reinitialize after first sentence to prevent hanging bug
                    print(f"🔄 Reinitializing TTS engine for sentence {i+1} (prevents hanging)")
                    self._initialize_engine()

                if not self.engine:
                    print(f"⚠️ TTS engine not available for sentence {i+1}, skipping...")
                    continue

                try:
                    # Generate audio for this sentence with timeout protection
                    print(f"🔄 Starting TTS conversion for sentence {i+1}...")
                    self.engine.save_to_file(sentence, temp_file)

                    # Add timeout protection for runAndWait()
                    import threading
                    import time

                    conversion_complete = threading.Event()
                    conversion_error = None

                    def run_tts():
                        nonlocal conversion_error
                        try:
                            self.engine.runAndWait()
                            conversion_complete.set()
                        except Exception as e:
                            conversion_error = e
                            conversion_complete.set()

                    # Start TTS in separate thread with timeout
                    tts_thread = threading.Thread(target=run_tts, daemon=True)
                    tts_thread.start()

                    # Wait for completion with timeout (30 seconds max per sentence)
                    if conversion_complete.wait(timeout=30):
                        if conversion_error:
                            raise conversion_error

                        if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:
                            audio_segment = AudioSegment.from_wav(temp_file)
                            audio_segments.append({
                                'audio': audio_segment,
                                'text': sentence,
                                'index': i
                            })
                            self._sentence_count += 1
                            print(f"✅ Sentence {i+1} converted successfully")
                        else:
                            print(f"⚠️ No audio generated for sentence {i+1}")
                    else:
                        print(f"⚠️ Timeout: Sentence {i+1} conversion took too long, skipping...")
                        # Force reinitialize after timeout
                        self._initialize_engine()

                except Exception as e:
                    print(f"❌ Error converting sentence {i+1}: {e}")
                    # Try to reinitialize and continue with next sentence
                    self._initialize_engine()
                    continue
            
            if not audio_segments:
                print("No audio segments were generated.")
                return ""
            
            # Combine audio segments with timing adjustments
            final_audio = self._combine_with_timing(
                audio_segments, timing_info, target_duration
            )
            
            # Export final audio
            output_path = base_path / "voiceover.mp3"
            final_audio.export(str(output_path), format="mp3")
            
            # Save timing analysis
            self._save_timing_analysis(audio_segments, final_audio, base_path)
            
            print(f"✅ Timed audio generated: {output_path}")
            print(f"📊 Final duration: {len(final_audio) / 1000:.2f} seconds")
            
            return str(output_path)
    
    def _extract_speech_sentences(self, text: str) -> List[str]:
        """Extract sentences suitable for speech synthesis"""
        # Remove code blocks
        text = re.sub(r'```.*?```', '', text, flags=re.DOTALL)
        text = re.sub(r'`.*?`', '', text)
        
        # Remove timing cues and comments
        text = re.sub(r'\[PAUSE\]', '', text)
        text = re.sub(r'\[.*?\]', '', text)
        text = re.sub(r'#.*$', '', text, flags=re.MULTILINE)
        
        # Remove markdown headers
        text = re.sub(r'^#+\s+', '', text, flags=re.MULTILINE)
        
        # Remove URLs and email addresses
        text = re.sub(r'http[s]?://\S+', '', text)
        text = re.sub(r'\S+@\S+', '', text)
        
        # Clean up special characters
        text = re.sub(r'[*_#\[\]()]', '', text)
        
        # Split into sentences
        sentences = re.split(r'[.!?]+', text)
        
        # Filter and clean sentences
        clean_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            # Skip short, empty, or code-like sentences
            if (len(sentence) > 10 and
                not re.match(r'^[A-Z_]+$', sentence) and
                not re.match(r'^\d+$', sentence) and
                not sentence.startswith(('def ', 'class ', 'import ', 'from '))):
                clean_sentences.append(sentence)
        
        return clean_sentences
    
    def _combine_with_timing(self, audio_segments: List[Dict], 
                           timing_info: Dict = None, target_duration: float = None) -> AudioSegment:
        """Combine audio segments with precise timing"""
        
        if not audio_segments:
            return AudioSegment.empty()
        
        # Calculate current total duration
        current_duration = sum(len(seg['audio']) for seg in audio_segments)
        current_duration += (len(audio_segments) - 1) * 500  # Default pauses
        current_duration_seconds = current_duration / 1000
        
        print(f"📊 Current audio duration: {current_duration_seconds:.2f}s")
        
        # If we have a target duration, adjust timing
        if target_duration:
            print(f"📊 Target duration: {target_duration}s")
            
            if abs(current_duration_seconds - target_duration) > 2.0:
                # Significant difference - adjust pauses
                return self._adjust_timing_to_target(audio_segments, target_duration)
        
        # Default combination with standard pauses
        combined_audio = AudioSegment.empty()
        
        for i, segment in enumerate(audio_segments):
            combined_audio += segment['audio']
            
            # Add pause between sentences (except after the last one)
            if i < len(audio_segments) - 1:
                pause_duration = 500  # 500ms default pause
                combined_audio += AudioSegment.silent(duration=pause_duration)
        
        return combined_audio
    
    def _adjust_timing_to_target(self, audio_segments: List[Dict], 
                               target_duration: float) -> AudioSegment:
        """Adjust audio timing to match target duration"""
        
        # Calculate speech duration (without pauses)
        speech_duration = sum(len(seg['audio']) for seg in audio_segments) / 1000
        
        # Calculate available time for pauses
        available_pause_time = target_duration - speech_duration
        
        if available_pause_time < 0:
            print("⚠️ Speech is longer than target duration - cannot fit!")
            # Speed up slightly if possible
            combined_audio = AudioSegment.empty()
            speedup_factor = speech_duration / (target_duration * 0.95)  # Leave 5% for minimal pauses
            
            for i, segment in enumerate(audio_segments):
                # Speed up audio slightly
                sped_up = segment['audio'].speedup(playback_speed=speedup_factor)
                combined_audio += sped_up
                
                # Add minimal pause
                if i < len(audio_segments) - 1:
                    combined_audio += AudioSegment.silent(duration=100)  # 100ms minimal pause
            
            return combined_audio
        
        # Distribute pause time evenly
        num_pauses = len(audio_segments) - 1
        if num_pauses > 0:
            pause_duration = (available_pause_time * 1000) / num_pauses  # Convert to ms
            pause_duration = max(200, min(2000, pause_duration))  # Clamp between 200ms and 2s
        else:
            pause_duration = 500
        
        print(f"📊 Adjusted pause duration: {pause_duration:.0f}ms")
        
        # Combine with adjusted pauses
        combined_audio = AudioSegment.empty()
        
        for i, segment in enumerate(audio_segments):
            combined_audio += segment['audio']
            
            if i < len(audio_segments) - 1:
                combined_audio += AudioSegment.silent(duration=int(pause_duration))
        
        return combined_audio
    
    def _save_timing_analysis(self, audio_segments: List[Dict], 
                            final_audio: AudioSegment, base_path: Path):
        """Save detailed timing analysis for debugging"""
        
        timing_data = {
            "total_duration_seconds": len(final_audio) / 1000,
            "sentence_count": len(audio_segments),
            "segments": []
        }
        
        current_time = 0
        for segment in audio_segments:
            segment_duration = len(segment['audio']) / 1000
            timing_data["segments"].append({
                "text": segment['text'],
                "start_time": current_time,
                "duration": segment_duration,
                "end_time": current_time + segment_duration
            })
            current_time += segment_duration + 0.5  # Add pause time
        
        with open(base_path / "audio_timing.json", "w", encoding="utf-8") as f:
            json.dump(timing_data, f, indent=2)
        
        print(f"📁 Audio timing analysis saved to: {base_path / 'audio_timing.json'}")


def generate_synchronized_audio(script: str, job_id: str, 
                              timing_info: Dict = None, target_duration: float = None) -> str:
    """Main function to generate synchronized audio"""
    tts = SynchronizedTTS()
    return tts.generate_timed_audio(script, job_id, timing_info, target_duration)


if __name__ == "__main__":
    # Test the synchronized TTS
    test_script = """
    Let's explore the concept of derivatives. A derivative represents the rate of change of a function.
    We can visualize this as the slope of a tangent line. This gives us powerful insights into how functions behave.
    """
    
    test_job_id = "sync_tts_test"
    
    try:
        audio_path = generate_synchronized_audio(
            test_script, test_job_id, target_duration=15.0
        )
        print(f"✅ Synchronized TTS test successful: {audio_path}")
    except Exception as e:
        print(f"❌ Test failed: {e}")
