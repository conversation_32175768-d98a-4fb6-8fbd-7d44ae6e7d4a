#!/usr/bin/env python3
"""
Debug script to identify TTS issues with second sentence conversion
"""

import os
import sys
import tempfile
import traceback
from pathlib import Path

# Add the app directory to the path
sys.path.append('app')

try:
    from synchronized_tts import SynchronizedTTS
    print("✅ Successfully imported SynchronizedTTS")
except ImportError as e:
    print(f"❌ Failed to import SynchronizedTTS: {e}")
    sys.exit(1)

def test_tts_sentence_by_sentence():
    """Test TTS conversion sentence by sentence to identify where it fails"""
    
    # Test script with multiple sentences
    test_script = """
    Let's explore the concept of derivatives. A derivative represents the rate of change of a function.
    We can visualize this as the slope of a tangent line. This gives us powerful insights into how functions behave.
    The mathematical definition involves limits and infinitesimal changes.
    """
    
    print("🔍 Testing TTS sentence-by-sentence conversion...")
    print("=" * 60)
    
    try:
        # Initialize TTS system
        print("🎵 Initializing TTS system...")
        tts = SynchronizedTTS()
        print("✅ TTS system initialized successfully")
        
        # Extract sentences
        sentences = tts._extract_speech_sentences(test_script)
        print(f"📝 Extracted {len(sentences)} sentences:")
        for i, sentence in enumerate(sentences):
            print(f"  {i+1}. {sentence}")
        
        print("\n🎵 Testing individual sentence conversion...")
        
        # Test each sentence individually
        with tempfile.TemporaryDirectory() as temp_dir:
            for i, sentence in enumerate(sentences):
                print(f"\n--- Testing Sentence {i+1} ---")
                print(f"Text: {sentence}")
                
                temp_file = os.path.join(temp_dir, f"test_sentence_{i:03d}.wav")
                
                try:
                    print(f"🔄 Converting to audio...")
                    
                    # This is where the issue likely occurs
                    tts.engine.save_to_file(sentence, temp_file)
                    print(f"✅ save_to_file() completed")
                    
                    print(f"🔄 Running engine.runAndWait()...")
                    tts.engine.runAndWait()
                    print(f"✅ runAndWait() completed")
                    
                    # Check if file was created
                    if os.path.exists(temp_file):
                        file_size = os.path.getsize(temp_file)
                        print(f"✅ Audio file created: {file_size} bytes")
                        
                        if file_size == 0:
                            print("⚠️ WARNING: Audio file is empty!")
                        else:
                            print(f"✅ Sentence {i+1} converted successfully")
                    else:
                        print(f"❌ Audio file not created for sentence {i+1}")
                        
                except Exception as e:
                    print(f"❌ ERROR converting sentence {i+1}: {e}")
                    print(f"Error type: {type(e).__name__}")
                    traceback.print_exc()
                    
                    # This is likely where it gets stuck
                    if i == 1:  # Second sentence (index 1)
                        print("🚨 FOUND THE ISSUE: Second sentence conversion failed!")
                        return False
        
        print("\n✅ All sentences converted successfully!")
        return True
        
    except Exception as e:
        print(f"❌ TTS test failed: {e}")
        traceback.print_exc()
        return False

def test_pyttsx3_engine():
    """Test the underlying pyttsx3 engine directly"""
    print("\n🔍 Testing pyttsx3 engine directly...")
    print("=" * 60)
    
    try:
        import pyttsx3
        
        print("🔄 Initializing pyttsx3 engine...")
        engine = pyttsx3.init()
        print("✅ Engine initialized")
        
        # Test basic properties
        voices = engine.getProperty('voices')
        print(f"📢 Available voices: {len(voices)}")
        for i, voice in enumerate(voices[:3]):  # Show first 3
            print(f"  {i+1}. {voice.name} ({voice.id})")
        
        rate = engine.getProperty('rate')
        volume = engine.getProperty('volume')
        print(f"📊 Current rate: {rate}, volume: {volume}")
        
        # Test simple conversion
        test_sentences = [
            "This is the first sentence.",
            "This is the second sentence.",
            "This is the third sentence."
        ]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            for i, sentence in enumerate(test_sentences):
                print(f"\n🔄 Testing sentence {i+1}: {sentence}")
                temp_file = os.path.join(temp_dir, f"direct_test_{i}.wav")
                
                try:
                    engine.save_to_file(sentence, temp_file)
                    engine.runAndWait()
                    
                    if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:
                        print(f"✅ Direct test {i+1} successful")
                    else:
                        print(f"❌ Direct test {i+1} failed - no audio file")
                        
                except Exception as e:
                    print(f"❌ Direct test {i+1} error: {e}")
                    if i == 1:  # Second sentence
                        print("🚨 CONFIRMED: Issue occurs on second sentence!")
                        return False
        
        print("✅ Direct pyttsx3 test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Direct pyttsx3 test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚨 TTS DEBUG TOOL - Finding Second Sentence Issue")
    print("=" * 60)
    
    # Test 1: Direct pyttsx3 engine
    success1 = test_pyttsx3_engine()
    
    # Test 2: SynchronizedTTS class
    success2 = test_tts_sentence_by_sentence()
    
    print("\n" + "=" * 60)
    print("🔍 DEBUG RESULTS:")
    print(f"Direct pyttsx3 test: {'✅ PASSED' if success1 else '❌ FAILED'}")
    print(f"SynchronizedTTS test: {'✅ PASSED' if success2 else '❌ FAILED'}")
    
    if not success1 or not success2:
        print("\n🚨 ISSUE IDENTIFIED:")
        print("The TTS system is failing on the second sentence conversion.")
        print("This is likely a pyttsx3 engine state issue.")
        print("\nPOSSIBLE SOLUTIONS:")
        print("1. Reinitialize the engine after each sentence")
        print("2. Add delays between conversions")
        print("3. Use a different TTS engine")
        print("4. Process sentences in separate processes")
    else:
        print("\n✅ No issues found - TTS system working correctly")
