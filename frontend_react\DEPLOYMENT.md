# Vercel Deployment Guide

## Prerequisites

1. **Backend Deployed**: Deploy your Python backend first to get the production API URL
2. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
3. **GitHub Repository**: Push your code to GitHub

## Deployment Steps

### 1. Deploy Backend First

Deploy your Python backend to a service like:
- **Railway**: Easy Python deployment
- **Render**: Free tier available
- **Heroku**: Popular choice
- **Google Cloud Run**: Scalable option

Get the production URL (e.g., `https://your-app.railway.app`)

### 2. Update Environment Variables

Edit `frontend_react/.env.production`:
```env
REACT_APP_API_URL=https://your-actual-backend-url.com
```

### 3. Deploy to Vercel

#### Option A: Vercel CLI
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy from frontend directory
cd frontend_react
vercel

# Follow prompts:
# - Set up and deploy? Y
# - Which scope? (your account)
# - Link to existing project? N
# - Project name: 3blue1brown-video-generator
# - Directory: ./
```

#### Option B: Vercel Dashboard
1. Go to [vercel.com/dashboard](https://vercel.com/dashboard)
2. Click "New Project"
3. Import your GitHub repository
4. Set **Root Directory** to `frontend_react`
5. Framework Preset: Create React App (auto-detected)
6. Build Command: `npm run build`
7. Output Directory: `build`
8. Add Environment Variable:
   - Name: `REACT_APP_API_URL`
   - Value: `https://your-backend-url.com`

### 4. Configure Backend CORS

Update your Python backend to allow requests from your Vercel domain:

```python
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # Development
        "https://your-vercel-app.vercel.app",  # Production
        "https://your-custom-domain.com"  # Custom domain if any
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### 5. Test Deployment

1. Visit your Vercel URL
2. Test video generation
3. Check browser console for any API errors
4. Verify downloads work correctly

## Environment Variables

The app uses these environment variables:

- **Development**: `REACT_APP_API_URL=http://localhost:8000`
- **Production**: `REACT_APP_API_URL=https://your-backend-url.com`

## Troubleshooting

### Common Issues

1. **API calls fail**: Check CORS configuration on backend
2. **Environment variables not working**: Ensure they start with `REACT_APP_`
3. **Build fails**: Check for any import errors or missing dependencies
4. **Downloads don't work**: Verify backend URL and file endpoints

### Vercel Logs

Check deployment logs in Vercel dashboard:
1. Go to your project
2. Click on a deployment
3. View "Functions" and "Build Logs" tabs

## Custom Domain (Optional)

1. In Vercel dashboard, go to project settings
2. Click "Domains"
3. Add your custom domain
4. Update DNS records as instructed
5. Update backend CORS to include new domain

## Performance Optimization

The `vercel.json` configuration includes:
- Static asset caching (1 year)
- SPA routing support
- Optimized build settings

Your app should achieve excellent performance scores on Vercel!
