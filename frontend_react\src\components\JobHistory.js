import React, { useState } from 'react';
import { Clock, Download, CheckCircle, AlertCircle, Loader, Play, PlayCircle, Eye, EyeOff } from 'lucide-react';
import { API_ENDPOINTS } from '../config/api';
import './JobHistory.css';

const JobHistory = ({ jobs, onJobUpdate }) => {
  const [expandedJobs, setExpandedJobs] = useState(new Set());
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={16} className="status-icon completed" />;
      case 'failed':
        return <AlertCircle size={16} className="status-icon failed" />;
      case 'processing':
        return <Loader size={16} className="status-icon processing spinning" />;
      default:
        return <Clock size={16} className="status-icon pending" />;
    }
  };

  const toggleJobExpansion = (jobId) => {
    const newExpanded = new Set(expandedJobs);
    if (newExpanded.has(jobId)) {
      newExpanded.delete(jobId);
    } else {
      newExpanded.add(jobId);
    }
    setExpandedJobs(newExpanded);
  };

  const handleDownload = async (jobId, fileType) => {
    try {
      const response = await fetch(API_ENDPOINTS.download(jobId, fileType));
      if (!response.ok) throw new Error('Download failed');
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Create filename
      const extensions = {
        video: '.mp4',
        complete_video: '.mp4',
        audio: '.mp3',
        script: '.txt',
        code: '.py',
        generation_info: '.json'
      };
      const filename = `${fileType}_${jobId}${extensions[fileType] || ''}`;
      
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Download failed:', err);
      alert('Download failed. Please try again.');
    }
  };

  if (jobs.length === 0) {
    return (
      <div className="job-history">
        <div className="card">
          <div className="empty-state">
            <Play size={48} className="empty-icon" />
            <h3>No videos generated yet</h3>
            <p>Start by generating your first educational video!</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="job-history">
      <div className="card">
        <h2>Generation History</h2>
        <p className="subtitle">
          View and download your previously generated videos
        </p>

        <div className="jobs-list">
          {jobs.map((job) => (
            <div key={job.id} className={`job-item ${job.status}`}>
              <div className="job-main">
                <div className="job-info">
                  <div className="job-title">
                    {getStatusIcon(job.status)}
                    <h4>{job.topic}</h4>
                  </div>
                  <div className="job-meta">
                    <span className="job-id">ID: {job.id}</span>
                    <span className="job-date">{formatDate(job.createdAt)}</span>
                    <span className="job-format">{job.format.toUpperCase()}</span>
                  </div>
                  {job.progress && (
                    <div className="job-progress">
                      <span>{job.progress}</span>
                    </div>
                  )}
                  {job.error && (
                    <div className="job-error">
                      <AlertCircle size={14} />
                      {job.error}
                    </div>
                  )}
                </div>

                {job.status === 'completed' && job.files && Object.keys(job.files).length > 0 && (
                  <div className="job-downloads">
                    <div className="job-actions">
                      <button
                        onClick={() => toggleJobExpansion(job.id)}
                        className="preview-toggle-btn"
                        title={expandedJobs.has(job.id) ? "Hide preview" : "Show preview"}
                      >
                        {expandedJobs.has(job.id) ? <EyeOff size={14} /> : <Eye size={14} />}
                        {expandedJobs.has(job.id) ? 'Hide' : 'Preview'}
                      </button>

                      <div className="download-buttons">
                        {Object.entries(job.files)
                          .sort(([a], [b]) => {
                            // Prioritize complete_video first
                            if (a === 'complete_video') return -1;
                            if (b === 'complete_video') return 1;
                            return 0;
                          })
                          .map(([fileType, path]) => {
                            const fileLabels = {
                              complete_video: 'Complete Video',
                              video: 'Video Only',
                              audio: 'Audio Only',
                              script: 'Script',
                              code: 'Code',
                              generation_info: 'Info'
                            };

                            const label = fileLabels[fileType] || fileType;
                            const isPrimary = fileType === 'complete_video';

                            return (
                              <button
                                key={fileType}
                                onClick={() => handleDownload(job.id, fileType)}
                                className={`download-btn ${isPrimary ? 'primary' : ''}`}
                                title={`Download ${label}`}
                              >
                                <Download size={14} />
                                {label}
                              </button>
                            );
                          })}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Expanded Preview Section */}
              {expandedJobs.has(job.id) && job.status === 'completed' && job.files && (
                <div className="job-preview-section">
                  {/* Complete Video Preview (Priority) */}
                  {job.files.complete_video && (
                    <div className="preview-item complete-video-item">
                      <h5><PlayCircle size={16} /> Complete Video (With Audio)</h5>
                      <div className="video-container">
                        <video
                          controls
                          className="preview-video complete-video"
                          preload="metadata"
                        >
                          <source src={`/download/${job.id}/complete_video`} type="video/mp4" />
                          Your browser does not support the video tag.
                        </video>
                      </div>
                      <p className="video-description">
                        🎬 Final video with synchronized audio and animations
                      </p>
                    </div>
                  )}

                  {/* Separate Video and Audio (if complete video not available or as additional options) */}
                  {job.files.video && (
                    <div className="preview-item">
                      <h5><PlayCircle size={16} /> {job.files.complete_video ? 'Video Only (No Audio)' : 'Video'}</h5>
                      <div className="video-container">
                        <video
                          controls
                          className="preview-video"
                          preload="metadata"
                        >
                          <source src={`/download/${job.id}/video`} type="video/mp4" />
                          Your browser does not support the video tag.
                        </video>
                      </div>
                    </div>
                  )}

                  {job.files.audio && (
                    <div className="preview-item">
                      <h5>🔊 Audio Only</h5>
                      <div className="audio-container">
                        <audio
                          controls
                          className="preview-audio"
                          preload="metadata"
                        >
                          <source src={`/download/${job.id}/audio`} type="audio/mpeg" />
                          Your browser does not support the audio tag.
                        </audio>
                      </div>
                    </div>
                  )}

                  {/* Script Preview */}
                  {job.files.script && (
                    <div className="preview-item">
                      <h5>📝 Script</h5>
                      <div className="script-container">
                        <iframe
                          src={`/download/${job.id}/script`}
                          className="preview-script"
                          title={`Script for ${job.topic}`}
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default JobHistory;
