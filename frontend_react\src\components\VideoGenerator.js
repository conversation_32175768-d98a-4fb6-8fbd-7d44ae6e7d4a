import React, { useState } from 'react';
import axios from 'axios';
import { Play, Loader, AlertCircle, CheckCircle, Download, PlayCircle, Volume2 } from 'lucide-react';
import { API_ENDPOINTS } from '../config/api';
import './VideoGenerator.css';

const VideoGenerator = ({ onJobCreate, onJobUpdate }) => {
  const [topic, setTopic] = useState('');
  const [format, setFormat] = useState('mp4');
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentJob, setCurrentJob] = useState(null);
  const [error, setError] = useState('');
  const [useCustomScript, setUseCustomScript] = useState(false);
  const [customScript, setCustomScript] = useState('');

  const exampleTopics = [
    'derivatives and tangent lines',
    'quadratic functions and parabolas',
    'area under the curve',
    'vector addition and subtraction',
    'trigonometric identities',
    'logarithmic functions',
    'matrix multiplication',
    'Fourier transform basics'
  ];

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!topic.trim()) {
      setError('Please enter a mathematical topic');
      return;
    }

    if (useCustomScript && !customScript.trim()) {
      setError('Please enter a custom script or switch to auto-generate mode');
      return;
    }

    setIsGenerating(true);
    setError('');
    setCurrentJob(null);

    try {
      // Prepare request data
      const requestData = {
        topic: topic.trim(),
        output_format: format
      };

      // Add custom script if provided
      if (useCustomScript && customScript.trim()) {
        requestData.custom_script = customScript.trim();
      }

      // Start generation
      const response = await axios.post(API_ENDPOINTS.generate, requestData);

      const job = {
        id: response.data.job_id,
        topic: topic.trim(),
        format,
        status: 'processing',
        progress: 'Starting generation...',
        createdAt: new Date().toISOString(),
        files: {}
      };

      setCurrentJob(job);
      onJobCreate(job);

      // Monitor progress
      monitorJob(job.id);

    } catch (err) {
      setError(err.response?.data?.detail || 'Failed to start generation');
      setIsGenerating(false);
    }
  };

  const monitorJob = async (jobId) => {
    const maxAttempts = 60; // 5 minutes max
    
    for (let i = 0; i < maxAttempts; i++) {
      try {
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
        
        const response = await axios.get(API_ENDPOINTS.status(jobId));
        const data = response.data;

        const updates = {
          status: data.status,
          progress: data.progress,
          files: data.files || {}
        };

        if (data.error) {
          updates.error = data.error;
        }

        setCurrentJob(prev => ({ ...prev, ...updates }));
        onJobUpdate(jobId, updates);

        if (data.status === 'completed') {
          setIsGenerating(false);
          return;
        } else if (data.status === 'failed') {
          setError(data.error || 'Generation failed');
          setIsGenerating(false);
          return;
        }

      } catch (err) {
        console.error('Status check failed:', err);
        // Continue monitoring unless it's a critical error
      }
    }

    setError('Generation timed out');
    setIsGenerating(false);
  };

  const handleDownload = async (jobId, fileType) => {
    try {
      const response = await axios.get(API_ENDPOINTS.download(jobId, fileType), {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      
      // Get filename from response headers or create one
      const contentDisposition = response.headers['content-disposition'];
      let filename = `${fileType}_${jobId}`;
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      } else {
        // Add appropriate extension
        const extensions = {
          video: '.mp4',
          complete_video: '.mp4',
          audio: '.mp3',
          script: '.txt',
          code: '.py',
          generation_info: '.json'
        };
        filename += extensions[fileType] || '';
      }
      
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Download failed:', err);
      setError('Download failed');
    }
  };

  return (
    <div className="video-generator">
      <div className="card">
        <h2>Generate Educational Video</h2>
        <p className="subtitle">
          Create 3Blue1Brown-style mathematical animations with AI-generated narration
        </p>

        <form onSubmit={handleSubmit} className="generator-form">
          <div className="form-group">
            <label htmlFor="topic">Mathematical Topic</label>
            <input
              id="topic"
              type="text"
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
              placeholder="e.g., derivatives and tangent lines"
              disabled={isGenerating}
              className="topic-input"
            />
            
            <div className="example-topics">
              <span>Examples:</span>
              {exampleTopics.slice(0, 4).map((example, index) => (
                <button
                  key={index}
                  type="button"
                  className="example-tag"
                  onClick={() => setTopic(example)}
                  disabled={isGenerating}
                >
                  {example}
                </button>
              ))}
            </div>
          </div>

          {/* Script Generation Mode */}
          <div className="form-group">
            <label className="script-mode-label">Script Generation</label>
            <div className="script-mode-options">
              <label className="radio-option">
                <input
                  type="radio"
                  name="scriptMode"
                  checked={!useCustomScript}
                  onChange={() => setUseCustomScript(false)}
                  disabled={isGenerating}
                />
                <span>Auto-generate script with AI</span>
              </label>
              <label className="radio-option">
                <input
                  type="radio"
                  name="scriptMode"
                  checked={useCustomScript}
                  onChange={() => setUseCustomScript(true)}
                  disabled={isGenerating}
                />
                <span>Provide custom script</span>
              </label>
            </div>
          </div>

          {/* Custom Script Input */}
          {useCustomScript && (
            <div className="form-group">
              <label htmlFor="customScript">Custom Script</label>
              <textarea
                id="customScript"
                value={customScript}
                onChange={(e) => setCustomScript(e.target.value)}
                placeholder="Enter your educational script here. The AI will generate synchronized animations and audio for your script..."
                disabled={isGenerating}
                className="custom-script-input"
                rows={6}
              />
              <div className="script-tips">
                <p><strong>Tips for better results:</strong></p>
                <ul>
                  <li>Write in a conversational, educational tone</li>
                  <li>Include mathematical concepts and visual descriptions</li>
                  <li>Keep sentences clear and well-paced for narration</li>
                  <li>Aim for 60-90 seconds of speaking time</li>
                </ul>
              </div>
            </div>
          )}

          <div className="form-group">
            <label htmlFor="format">Output Format</label>
            <select
              id="format"
              value={format}
              onChange={(e) => setFormat(e.target.value)}
              disabled={isGenerating}
              className="format-select"
            >
              <option value="mp4">MP4 Video (Recommended)</option>
              <option value="gif">GIF Animation</option>
            </select>
          </div>

          <button
            type="submit"
            disabled={isGenerating || !topic.trim()}
            className="generate-button"
          >
            {isGenerating ? (
              <>
                <Loader className="spinning" size={20} />
                Generating...
              </>
            ) : (
              <>
                <Play size={20} />
                Generate Video
              </>
            )}
          </button>
        </form>

        {error && (
          <div className="error-message">
            <AlertCircle size={20} />
            {error}
          </div>
        )}

        {currentJob && (
          <div className="current-job">
            <div className="job-header">
              <h3>Current Generation</h3>
              <div className={`status-badge ${currentJob.status}`}>
                {currentJob.status === 'processing' && <Loader className="spinning" size={16} />}
                {currentJob.status === 'completed' && <CheckCircle size={16} />}
                {currentJob.status === 'failed' && <AlertCircle size={16} />}
                {currentJob.status}
              </div>
            </div>

            <div className="job-details">
              <p><strong>Topic:</strong> {currentJob.topic}</p>
              <p><strong>Progress:</strong> {currentJob.progress}</p>
              <p><strong>Job ID:</strong> {currentJob.id}</p>
            </div>

            {currentJob.status === 'completed' && Object.keys(currentJob.files).length > 0 && (
              <div className="results-section">
                {/* Complete Video Preview (Priority) */}
                {currentJob.files.complete_video && (
                  <div className="complete-video-preview">
                    <h4><PlayCircle size={20} /> Complete Video (With Audio)</h4>
                    <div className="video-container">
                      <video
                        controls
                        className="preview-video complete-video"
                        preload="metadata"
                      >
                        <source src={`/download/${currentJob.id}/complete_video`} type="video/mp4" />
                        Your browser does not support the video tag.
                      </video>
                    </div>
                    <p className="video-description">
                      🎬 This is the final video with synchronized audio and animations
                    </p>
                  </div>
                )}

                {/* Separate Video and Audio (if complete video not available) */}
                {!currentJob.files.complete_video && (
                  <>
                    {/* Video Preview */}
                    {currentJob.files.video && (
                      <div className="video-preview">
                        <h4><PlayCircle size={20} /> Video Preview (No Audio)</h4>
                        <div className="video-container">
                          <video
                            controls
                            className="preview-video"
                            preload="metadata"
                          >
                            <source src={`/download/${currentJob.id}/video`} type="video/mp4" />
                            Your browser does not support the video tag.
                          </video>
                        </div>
                      </div>
                    )}

                    {/* Audio Preview */}
                    {currentJob.files.audio && (
                      <div className="audio-preview">
                        <h4><Volume2 size={20} /> Audio Preview</h4>
                        <div className="audio-container">
                          <audio
                            controls
                            className="preview-audio"
                            preload="metadata"
                          >
                            <source src={`/download/${currentJob.id}/audio`} type="audio/mpeg" />
                            Your browser does not support the audio tag.
                          </audio>
                        </div>
                      </div>
                    )}
                  </>
                )}

                {/* Script Preview */}
                {currentJob.files.script && (
                  <div className="script-preview">
                    <h4>📝 Generated Script</h4>
                    <div className="script-container">
                      <iframe
                        src={`/download/${currentJob.id}/script`}
                        className="preview-script"
                        title="Generated Script"
                      />
                    </div>
                  </div>
                )}

                <div className="download-section">
                  <h4>Download Files</h4>
                  <div className="download-grid">
                    {Object.entries(currentJob.files).map(([fileType, path]) => {
                      // Custom labels for better UX
                      const fileLabels = {
                        complete_video: 'Complete Video (MP4)',
                        video: 'Video Only (MP4)',
                        audio: 'Audio Only (MP3)',
                        script: 'Script (TXT)',
                        code: 'Manim Code (PY)',
                        generation_info: 'Generation Info (JSON)'
                      };

                      const label = fileLabels[fileType] || fileType.charAt(0).toUpperCase() + fileType.slice(1);
                      const isPrimary = fileType === 'complete_video';

                      return (
                        <button
                          key={fileType}
                          onClick={() => handleDownload(currentJob.id, fileType)}
                          className={`download-button ${isPrimary ? 'primary' : ''}`}
                        >
                          <Download size={16} />
                          {label}
                          {isPrimary && <span className="primary-badge">Recommended</span>}
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoGenerator;
