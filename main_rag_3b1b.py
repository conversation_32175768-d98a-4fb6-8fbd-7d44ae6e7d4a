"""
Complete Audio-First 3Blue1Brown Video Generator
Fully automated pipeline - user only provides topic
"""

import os
import sys
import uuid
from pathlib import Path
from dotenv import load_dotenv

# Import our audio-first modules
from app.llm_rag_audio_first import generate_audio_first_content
from app.manim_3b1b_runner import generate_video_3b1b, check_3b1b_manim_installation
from app.video_merger import merge_audio_video

# Load environment variables
load_dotenv()

def print_banner():
    """Print welcome banner"""
    print("=" * 70)
    print("🎬 COMPLETE AUDIO-FIRST 3BLUE1BROWN VIDEO GENERATOR")
    print("=" * 70)
    print("✨ Fully automated pipeline with natural speech pacing")
    print("🎯 RAG-enhanced validation for error-free code generation")
    print("🎵 Audio-first approach with perfect synchronization")
    print("🎥 Professional 3Blue1Brown style animations")
    print("📚 Enhanced with 3Blue1Brown codebase knowledge")
    print("=" * 70)
    print()

def validate_environment():
    """Validate that all required components are available"""
    print("🔍 Validating environment...")
    
    # Check API keys
    required_keys = ["GOOGLE_API_KEY", "GOOGLE_API_KEY2"]
    missing_keys = []
    
    for key in required_keys:
        if not os.getenv(key):
            missing_keys.append(key)
    
    if missing_keys:
        print(f"❌ Missing required API keys: {', '.join(missing_keys)}")
        print("Please set these in your .env file")
        return False
    
    # Check 3Blue1Brown manim installation
    try:
        manim_status = check_3b1b_manim_installation()
        # Handle both boolean and dict returns
        if isinstance(manim_status, bool):
            if not manim_status:
                print("❌ 3Blue1Brown manim not available")
                return False
        elif isinstance(manim_status, dict):
            if not manim_status.get("available", False):
                print("❌ 3Blue1Brown manim not available")
                print(f"Error: {manim_status.get('error', 'Unknown error')}")
                return False
    except Exception as e:
        print(f"❌ Error checking manim installation: {e}")
        return False
    
    print("✅ Environment validation passed")
    return True

def get_user_input():
    """Get topic from user with helpful prompts"""
    print("📝 TOPIC SELECTION")
    print("-" * 30)
    print("Enter a mathematical topic for your video. Examples:")
    print("• 'quadratic functions and parabolas'")
    print("• 'derivatives and rates of change'")
    print("• 'trigonometry and unit circle'")
    print("• 'integration by parts'")
    print("• 'complex numbers and Euler's formula'")
    print("• 'matrix multiplication and transformations'")
    print()
    
    while True:
        topic = input("🎯 Enter your topic: ").strip()
        if topic:
            return topic
        print("❌ Please enter a valid topic")

def generate_complete_video(topic: str):
    """
    Complete automated video generation pipeline
    
    Args:
        topic: Mathematical topic for the video
        
    Returns:
        dict: Results with file paths and success status
    """
    # Generate unique job ID
    job_id = f"video_{uuid.uuid4().hex[:8]}"
    
    results = {
        "success": False,
        "job_id": job_id,
        "topic": topic,
        "files": {},
        "timing": {},
        "errors": []
    }
    
    try:
        print(f"\n🚀 STARTING COMPLETE VIDEO GENERATION")
        print(f"📋 Topic: {topic}")
        print(f"🆔 Job ID: {job_id}")
        print("=" * 50)
        
        # Step 1: Generate RAG-enhanced audio-first content
        print("🎵 Step 1: Generating RAG-enhanced natural audio and synchronized code...")
        print("-" * 50)

        content_result = generate_audio_first_content(topic, job_id)
        
        if not content_result:
            results["errors"].append("Audio-first generation failed")
            return results
        
        # Construct file paths
        base_path = f"generated/{job_id}"
        audio_path = content_result["audio_path"]
        script_path = f"{base_path}/script.txt"
        code_path = f"{base_path}/manim_code.py"

        print(f"✅ Audio generated: {audio_path}")
        print(f"✅ Script generated: {script_path}")
        print(f"✅ Code generated: {code_path}")
        print(f"📊 Natural duration: {content_result['natural_duration']:.2f} seconds")
        print(f"📚 RAG Enhanced: {content_result.get('rag_enhanced', False)}")
        print(f"🎵 Audio Segments: {len(content_result.get('segments', []))}")

        results["files"]["audio"] = audio_path
        results["files"]["script"] = script_path
        results["files"]["code"] = code_path
        results["timing"]["natural_duration"] = content_result["natural_duration"]
        results["timing"]["rag_enhanced"] = content_result.get("rag_enhanced", False)
        results["timing"]["audio_segments"] = len(content_result.get("segments", []))
        print()
        
        # Step 2: Generate video with 3Blue1Brown manim
        print("🎬 Step 2: Generating video with 3Blue1Brown manim...")
        print("-" * 50)
        
        # Read the generated code
        with open(code_path, 'r', encoding='utf-8') as f:
            manim_code = f.read()
        
        video_path = generate_video_3b1b(manim_code, job_id, output_format="mp4")
        
        if not video_path:
            results["errors"].append("Video generation failed")
            return results
        
        print(f"✅ Video generated: {video_path}")
        results["files"]["video"] = video_path
        print()
        
        # Step 3: Merge audio and video
        print("🔗 Step 3: Merging audio and video...")
        print("-" * 50)
        
        final_video_path = merge_audio_video(
            content_result["audio_path"],
            video_path,
            job_id,
            output_name="complete_video"
        )
        
        print(f"✅ Final integrated video created: {final_video_path}")
        results["files"]["final_video"] = final_video_path
        results["success"] = True
        print()
        
        return results
        
    except Exception as e:
        error_msg = f"Pipeline error: {str(e)}"
        print(f"❌ {error_msg}")
        results["errors"].append(error_msg)
        return results

def print_results(results: dict):
    """Print final results summary"""
    print("🎯 GENERATION COMPLETE!")
    print("=" * 50)
    
    if results["success"]:
        print("✅ SUCCESS! Your video has been generated.")
        print()
        print("📁 Generated Files:")
        for file_type, file_path in results["files"].items():
            if file_type == "final_video":
                print(f"🎬 Final Video: {file_path}")
            elif file_type == "audio":
                print(f"🎵 Audio: {file_path}")
            elif file_type == "video":
                print(f"🎥 Video: {file_path}")
            elif file_type == "script":
                print(f"📝 Script: {file_path}")
            elif file_type == "code":
                print(f"🐍 Code: {file_path}")
        
        if "natural_duration" in results["timing"]:
            duration = results["timing"]["natural_duration"]
            print(f"⏱️ Duration: {duration:.2f} seconds ({duration/60:.1f} minutes)")

        if "rag_enhanced" in results["timing"]:
            rag_status = "✅ Enabled" if results["timing"]["rag_enhanced"] else "❌ Disabled"
            print(f"📚 RAG Enhancement: {rag_status}")

        if "audio_segments" in results["timing"]:
            segments = results["timing"]["audio_segments"]
            print(f"🎵 Audio Segments: {segments}")

        print()
        print("🎉 Your educational video is ready!")
        print(f"📂 All files saved in: generated/{results['job_id']}/")
        
    else:
        print("❌ GENERATION FAILED")
        print()
        print("🔍 Errors encountered:")
        for error in results["errors"]:
            print(f"   • {error}")
        print()
        print("💡 Please check your environment setup and try again.")

def main():
    """Main function - complete automated pipeline"""
    print_banner()
    
    # Validate environment
    if not validate_environment():
        print("\n❌ Environment validation failed. Please fix the issues above.")
        sys.exit(1)
    
    print()
    
    # Get user input
    topic = get_user_input()
    
    # Generate complete video
    results = generate_complete_video(topic)
    
    # Print results
    print_results(results)
    
    # Exit with appropriate code
    sys.exit(0 if results["success"] else 1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Generation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        sys.exit(1)
